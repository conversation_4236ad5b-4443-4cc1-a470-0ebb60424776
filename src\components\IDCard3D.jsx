import React, { useRef, useState, useEffect } from 'react'
import { Can<PERSON>, extend, useThree, use<PERSON>rame } from '@react-three/fiber'
import { Environment, Text, Center } from '@react-three/drei'
import { Physics, RigidBody, BallCollider, CuboidCollider, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import * as THREE from 'three'
import './IDCard3D.css'

// Extend the catalog to use MeshLine
extend({ MeshLineGeometry, MeshLineMaterial })

// ID Card Component
const IDCard = ({ position, rotation, ...props }) => {
  return (
    <group position={position} rotation={rotation} {...props}>
      {/* Card Base - Dark background like the reference */}
      <mesh>
        <boxGeometry args={[1.6, 2.25, 0.02]} />
        <meshPhysicalMaterial
          color="#1a1a1a"
          metalness={0.3}
          roughness={0.2}
          clearcoat={1}
          clearcoatRoughness={0.1}
        />
      </mesh>

      {/* Card Content */}
      <group position={[0, 0, 0.011]}>
        {/* Top Logo/Brand Area */}
        <mesh position={[-0.6, 0.9, 0]}>
          <boxGeometry args={[0.15, 0.15, 0.001]} />
          <meshBasicMaterial color="#00ff00" />
        </mesh>

        {/* Brand Text */}
        <Text
          position={[0.2, 0.9, 0]}
          fontSize={0.06}
          color="#888888"
          anchorX="left"
          anchorY="middle"
          font="/fonts/inter-medium.woff"
        >
          gateremark
        </Text>

        {/* Profile Photo Area - Larger like reference */}
        <mesh position={[0, 0.1, 0]}>
          <boxGeometry args={[0.8, 1.0, 0.001]} />
          <meshBasicMaterial color="#333333" />
        </mesh>

        {/* Profile Photo Border */}
        <mesh position={[0, 0.1, 0.001]}>
          <boxGeometry args={[0.75, 0.95, 0.001]} />
          <meshBasicMaterial color="#000000" />
        </mesh>

        {/* Simulated Photo Content */}
        <mesh position={[0, 0.2, 0.002]}>
          <circleGeometry args={[0.2, 32]} />
          <meshBasicMaterial color="#555555" />
        </mesh>

        {/* Face representation */}
        <Text
          position={[0, 0.2, 0.003]}
          fontSize={0.12}
          color="#888888"
          anchorX="center"
          anchorY="middle"
        >
          👤
        </Text>

        {/* Name at bottom of photo area */}
        <Text
          position={[0, -0.25, 0.002]}
          fontSize={0.06}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          font="/fonts/inter-bold.woff"
        >
          MARK GATERE
        </Text>

        {/* Title */}
        <Text
          position={[0, -0.35, 0.002]}
          fontSize={0.04}
          color="#888888"
          anchorX="center"
          anchorY="middle"
        >
          Software Developer
        </Text>

        {/* Bottom stripe like reference */}
        <mesh position={[0, -0.8, 0]}>
          <boxGeometry args={[1.5, 0.3, 0.001]} />
          <meshBasicMaterial color="#2a2a2a" />
        </mesh>

        {/* ID Number */}
        <Text
          position={[0, -0.8, 0.001]}
          fontSize={0.035}
          color="#00ff00"
          anchorX="center"
          anchorY="middle"
        >
          ID: MG-2024-001
        </Text>

        {/* Lanyard hole */}
        <mesh position={[0, 1.0, 0]}>
          <cylinderGeometry args={[0.05, 0.05, 0.03, 16]} />
          <meshBasicMaterial color="#666666" />
        </mesh>
      </group>
    </group>
  )
}

// Physics-based Lanyard Band Component
const LanyardBand = ({ maxSpeed = 50, minSpeed = 10 }) => {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()
  
  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()
  
  const segmentProps = {
    type: 'dynamic',
    canSleep: true,
    colliders: false,
    angularDamping: 2,
    linearDamping: 2
  }
  
  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()
  ]))
  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  // Create rope joints
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current?.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z
      })
    }
    
    if (fixed.current) {
      // Fix jitter when over pulling the card
      ;[j1, j2].forEach((ref) => {
        if (!ref.current.lerped) {
          ref.current.lerped = new THREE.Vector3().copy(ref.current.translation())
        }
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())))
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      })
      
      // Calculate catmull curve
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy(j2.current.lerped)
      curve.points[2].copy(j1.current.lerped)
      curve.points[3].copy(fixed.current.translation())
      band.current.geometry.setPoints(curve.getPoints(32))
      
      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z })
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody 
          position={[2, 0, 0]} 
          ref={card} 
          {...segmentProps} 
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => (e.target.releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => (
              e.target.setPointerCapture(e.pointerId),
              drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation())))
            )}
          >
            <IDCard />
          </group>
        </RigidBody>
      </group>
      
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial 
          color="white" 
          depthTest={false} 
          resolution={[width, height]} 
          lineWidth={1} 
        />
      </mesh>
    </>
  )
}

// Main 3D Scene Component
const IDCard3D = () => {
  return (
    <div className="card3d-container">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <LanyardBand />
        </Physics>
        <Environment preset="studio" />
      </Canvas>

    </div>
  )
}

export default IDCard3D
