import './ProfileCard.css'

const ProfileCard = () => {
  return (
    <div className="profile-card-container">
      <div className="profile-card">
        <div className="profile-header">
          <div className="profile-image">
            <img 
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face" 
              alt="Mark Gatere" 
              className="profile-photo"
            />
            <div className="status-indicator"></div>
          </div>
          <div className="profile-info">
            <h1 className="profile-name"><PERSON></h1>
            <p className="profile-title">Software Engineer</p>
            <div className="profile-status">
              <span className="status-dot"></span>
              Available for work
            </div>
          </div>
        </div>

        <div className="profile-details">
          <div className="detail-section">
            <h3>🚀 Specializations</h3>
            <div className="specializations">
              <span className="spec-tag">Full-Stack Development</span>
              <span className="spec-tag">React/Next.js</span>
              <span className="spec-tag">Node.js</span>
              <span className="spec-tag">TypeScript</span>
              <span className="spec-tag">3D Graphics</span>
              <span className="spec-tag">WebGL/Three.js</span>
            </div>
          </div>

          <div className="detail-section">
            <h3>💼 Experience</h3>
            <div className="experience-item">
              <div className="exp-company">TechCorp Inc.</div>
              <div className="exp-role">Senior Frontend Developer</div>
              <div className="exp-duration">2022 - Present</div>
            </div>
            <div className="experience-item">
              <div className="exp-company">StartupXYZ</div>
              <div className="exp-role">Full Stack Developer</div>
              <div className="exp-duration">2020 - 2022</div>
            </div>
          </div>

          <div className="detail-section">
            <h3>🎓 Education</h3>
            <div className="education-item">
              <div className="edu-degree">B.S. Computer Science</div>
              <div className="edu-school">University of Technology</div>
              <div className="edu-year">2015 - 2019</div>
            </div>
          </div>

          <div className="detail-section">
            <h3>📊 Stats</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-number">5+</div>
                <div className="stat-label">Years Experience</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">50+</div>
                <div className="stat-label">Projects</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">15+</div>
                <div className="stat-label">Technologies</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">100%</div>
                <div className="stat-label">Satisfaction</div>
              </div>
            </div>
          </div>

          <div className="detail-section">
            <h3>🌐 Connect</h3>
            <div className="social-links">
              <a href="mailto:<EMAIL>" className="social-link email">
                <span className="social-icon">📧</span>
                Email
              </a>
              <a href="https://linkedin.com/in/markgatere" className="social-link linkedin">
                <span className="social-icon">💼</span>
                LinkedIn
              </a>
              <a href="https://github.com/markgatere" className="social-link github">
                <span className="social-icon">🐙</span>
                GitHub
              </a>
              <a href="https://twitter.com/markgatere" className="social-link twitter">
                <span className="social-icon">🐦</span>
                Twitter
              </a>
            </div>
          </div>
        </div>

        <div className="profile-footer">
          <div className="terminal-hint">
            <span className="hint-icon">💻</span>
            <span>Try typing commands in the terminal →</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileCard
