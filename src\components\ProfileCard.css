.profile-card-container {
  width: 400px;
  min-width: 400px;
  height: 100vh;
  background: #0a0a0a;
  border-right: 2px solid #00ff00;
  overflow-y: auto;
  position: relative;
}

.profile-card-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 0, 0.02) 2px,
      rgba(0, 255, 0, 0.02) 4px
    );
  pointer-events: none;
  z-index: 1;
}

.profile-card {
  padding: 2rem;
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.profile-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(0, 255, 0, 0.2);
}

.profile-image {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.profile-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid #00ff00;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: #00ff00;
  border-radius: 50%;
  border: 3px solid #0a0a0a;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(0, 255, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0); }
}

.profile-name {
  color: #00ff00;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.profile-title {
  color: #888;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
}

.profile-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #00ff00;
  font-size: 0.9rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #00ff00;
  border-radius: 50%;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.profile-details {
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  color: #00ff00;
  font-size: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 255, 0, 0.3);
  padding-bottom: 0.5rem;
}

.specializations {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.spec-tag {
  background: rgba(0, 255, 0, 0.1);
  color: #00ff00;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid rgba(0, 255, 0, 0.3);
  transition: all 0.3s ease;
}

.spec-tag:hover {
  background: rgba(0, 255, 0, 0.2);
  transform: translateY(-2px);
}

.experience-item, .education-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 0, 0.05);
  border-left: 3px solid #00ff00;
  border-radius: 0 4px 4px 0;
}

.exp-company, .edu-degree {
  color: #00ff00;
  font-weight: 600;
  font-size: 0.9rem;
}

.exp-role, .edu-school {
  color: #ccc;
  font-size: 0.85rem;
  margin: 0.25rem 0;
}

.exp-duration, .edu-year {
  color: #888;
  font-size: 0.8rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 255, 0, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 0, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(0, 255, 0, 0.1);
  transform: translateY(-2px);
}

.stat-number {
  color: #00ff00;
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.stat-label {
  color: #888;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.social-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(0, 255, 0, 0.05);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 6px;
  color: #00ff00;
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(0, 255, 0, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 255, 0, 0.2);
}

.social-icon {
  font-size: 1rem;
}

.profile-footer {
  margin-top: auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 255, 0, 0.2);
}

.terminal-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  font-size: 0.85rem;
  text-align: center;
  justify-content: center;
  animation: hint-pulse 3s infinite;
}

@keyframes hint-pulse {
  0%, 70% { opacity: 0.7; }
  35% { opacity: 1; }
}

.hint-icon {
  font-size: 1.2rem;
}

/* Scrollbar styling */
.profile-card-container::-webkit-scrollbar {
  width: 6px;
}

.profile-card-container::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.profile-card-container::-webkit-scrollbar-thumb {
  background: #00ff00;
  border-radius: 3px;
}

.profile-card-container::-webkit-scrollbar-thumb:hover {
  background: #00aa00;
}

/* Responsive design */
@media (max-width: 768px) {
  .profile-card-container {
    width: 100%;
    min-width: unset;
    height: 50vh;
    border-right: none;
    border-bottom: 2px solid #00ff00;
  }
  
  .profile-card {
    padding: 1rem;
  }
  
  .profile-photo {
    width: 80px;
    height: 80px;
  }
  
  .profile-name {
    font-size: 1.4rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }
  
  .stat-item {
    padding: 0.5rem;
  }
  
  .stat-number {
    font-size: 1.2rem;
  }
  
  .social-links {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
