@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

.terminal-container {
  flex: 1;
  height: 100vh;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  color: #00ff00;
  overflow: hidden;
}

.terminal-header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-height: 40px;
}

.terminal-buttons {
  display: flex;
  gap: 0.5rem;
}

.terminal-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
}

.terminal-button.close {
  background: #ff5f56;
}

.terminal-button.minimize {
  background: #ffbd2e;
}

.terminal-button.maximize {
  background: #27ca3f;
}

.terminal-title {
  color: #888;
  font-size: 0.9rem;
  font-weight: 500;
}

.terminal-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: #000;
  position: relative;
}

.terminal-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 0, 0.03) 2px,
      rgba(0, 255, 0, 0.03) 4px
    );
  pointer-events: none;
  z-index: 1;
}

.terminal-output {
  position: relative;
  z-index: 2;
}

.terminal-line {
  margin-bottom: 0.25rem;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.terminal-line.command {
  color: #00ff00;
  font-weight: 500;
}

.terminal-line.command .prompt {
  color: #ff6b6b;
  margin-right: 0.5rem;
}

.terminal-line.output {
  color: #ffffff;
}

.terminal-line.success {
  color: #00ff00;
}

.terminal-line.info {
  color: #61dafb;
}

.terminal-line.error {
  color: #ff6b6b;
}

.terminal-line.typing {
  color: #ffeb3b;
}

.terminal-input-form {
  position: relative;
  z-index: 2;
  margin-top: 1rem;
}

.terminal-input-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-input-line .prompt {
  color: #ff6b6b;
  font-weight: 500;
  user-select: none;
}

.terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #00ff00;
  font-family: inherit;
  font-size: inherit;
  caret-color: transparent;
}

.terminal-input:disabled {
  opacity: 0.6;
}

.cursor {
  color: #00ff00;
  font-weight: bold;
}

.cursor.blinking {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Scrollbar styling */
.terminal-body::-webkit-scrollbar {
  width: 8px;
}

.terminal-body::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.terminal-body::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 4px;
}

.terminal-body::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Text selection */
.terminal-body ::selection {
  background: rgba(0, 255, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .terminal-container {
    font-size: 14px;
    height: 50vh;
  }

  .terminal-body {
    padding: 0.5rem;
  }

  .terminal-header {
    padding: 0.25rem 0.5rem;
  }

  .terminal-title {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .terminal-container {
    font-size: 12px;
  }
  
  .terminal-buttons {
    gap: 0.25rem;
  }
  
  .terminal-button {
    width: 10px;
    height: 10px;
  }
}

/* Matrix-like effect for special commands */
.terminal-line.matrix {
  color: #00ff00;
  text-shadow: 0 0 5px #00ff00;
  animation: matrix-glow 2s ease-in-out infinite alternate;
}

@keyframes matrix-glow {
  from { text-shadow: 0 0 5px #00ff00; }
  to { text-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00; }
}

/* Typing effect enhancement */
.terminal-line.typing .cursor {
  animation: none;
  opacity: 1;
}

/* Command history styling */
.terminal-line.command:hover {
  background: rgba(0, 255, 0, 0.1);
  padding: 0 0.25rem;
  margin: 0 -0.25rem;
  border-radius: 2px;
}

/* ASCII art and special content */
.terminal-line pre {
  margin: 0;
  font-family: inherit;
}

/* Link styling for terminal */
.terminal-link {
  color: #61dafb;
  text-decoration: underline;
  cursor: pointer;
}

.terminal-link:hover {
  color: #ffffff;
  text-shadow: 0 0 5px #61dafb;
}

/* Progress bar styling */
.progress-bar {
  display: inline-block;
  width: 20ch;
  background: #333;
  border: 1px solid #555;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #00ff00;
  transition: width 0.3s ease;
}

/* File tree styling */
.file-tree {
  font-family: inherit;
  color: #888;
}

.file-tree .folder {
  color: #61dafb;
}

.file-tree .file {
  color: #ffffff;
}

.file-tree .executable {
  color: #00ff00;
}

/* Boot sequence styling */
.boot-sequence {
  color: #ffeb3b;
  font-weight: 500;
}

.boot-sequence .ok {
  color: #00ff00;
}

.boot-sequence .fail {
  color: #ff6b6b;
}

/* Welcome message styling */
.terminal-line.success {
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

/* Enhanced cursor animation */
@keyframes cursor-pulse {
  0%, 50% {
    opacity: 1;
    transform: scale(1);
  }
  51%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

.cursor.blinking {
  animation: cursor-pulse 1.2s infinite;
}

/* Hover effects for terminal lines */
.terminal-line:hover {
  background: rgba(0, 255, 0, 0.05);
  padding: 0 0.5rem;
  margin: 0 -0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

/* Special styling for ASCII art */
.ascii-art {
  font-family: 'Courier New', monospace;
  line-height: 1.2;
  color: #00ff00;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

/* Loading animation */
@keyframes loading-dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

.loading::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

/* Terminal window glow effect */
.terminal-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00ff00, #0080ff, #00ff00);
  border-radius: 8px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(6px);
}

/* Enhanced terminal header */
.terminal-header {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-bottom: 2px solid #00ff00;
  box-shadow: 0 2px 10px rgba(0, 255, 0, 0.2);
}

/* Improved scrollbar */
.terminal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00ff00, #00aa00);
  border-radius: 4px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
}

.terminal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #00aa00, #00ff00);
}
